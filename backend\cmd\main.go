package main

import (
	"context"
	"fmt"
	"log"
	"xr-gallery/internal/config"
	"xr-gallery/internal/database"
	"xr-gallery/internal/handler"
	"xr-gallery/internal/middleware"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/app/server"
	"github.com/cloudwego/hertz/pkg/common/utils"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig(config.GetConfigPath())
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化数据库
	if err := database.InitDB(cfg.Database); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer database.CloseDB()

	// 创建Hertz服务器实例
	addr := fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port)
	h := server.Default(server.WithHostPorts(addr))

	// 添加CORS中间件
	h.Use(middleware.CORSMiddleware(cfg.CORS.AllowOrigins))

	// 注册图片代理路由
	h.GET("/api/img", handler.ImageProxyHandler)

	// 注册图库API路由
	h.GET("/api/gallery/list", handler.GetGalleryList)
	h.GET("/api/gallery/detail/:xrid", handler.GetGalleryDetail)

	// 基础路由测试
	h.GET("/ping", func(ctx context.Context, c *app.RequestContext) {
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(200, utils.H{
			"message":  "pong",
			"service":  "xr-gallery",
			"database": "connected",
		})
	})

	// 启动服务器
	log.Printf("XR Gallery server starting on %s", addr)
	h.Spin()
}
