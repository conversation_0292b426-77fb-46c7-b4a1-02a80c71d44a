package middleware

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
)

// CORSMiddleware CORS中间件
func CORSMiddleware(allowOrigins []string) app.HandlerFunc {
	return func(ctx context.Context, c *app.RequestContext) {
		// 设置CORS头
		origin := string(c.<PERSON>eader("Origin"))

		// 检查是否在允许的源列表中
		allowed := false
		for _, allowedOrigin := range allowOrigins {
			if allowedOrigin == "*" || allowedOrigin == origin {
				allowed = true
				break
			}
		}

		if allowed {
			c.<PERSON><PERSON>("Access-Control-Allow-Origin", origin)
		} else if len(allowOrigins) > 0 && allowOrigins[0] == "*" {
			c.<PERSON><PERSON>("Access-Control-Allow-Origin", "*")
		}

		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")
		c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")
		c.<PERSON><PERSON>("Access-Control-Max-Age", "86400")

		// 设置UTF-8编码
		c.Header("Content-Type", "application/json; charset=utf-8")

		// 处理预检请求
		if string(c.Method()) == "OPTIONS" {
			c.Status(200)
			return
		}

		// 继续处理请求
		c.Next(ctx)
	}
}
