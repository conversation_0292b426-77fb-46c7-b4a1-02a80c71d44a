/**
 * 设备检测工具函数
 * 用于判断用户设备类型，决定使用移动端还是桌面端组件
 */

/**
 * 检测是否为移动设备
 * @returns {boolean} 如果是移动设备返回true，否则返回false
 */
export function isMobileDevice() {
  // 检查用户代理字符串
  const userAgent = navigator.userAgent.toLowerCase()
  const mobileKeywords = [
    'android', 'webos', 'iphone', 'ipad', 'ipod', 
    'blackberry', 'windows phone', 'mobile', 'opera mini'
  ]
  
  const isMobileUA = mobileKeywords.some(keyword => userAgent.includes(keyword))
  
  // 检查屏幕宽度
  const screenWidth = window.innerWidth
  const isMobileScreen = screenWidth <= 768
  
  // 检查触摸支持
  const hasTouchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0
  
  // 综合判断：用户代理包含移动设备关键词 或 屏幕宽度小于768px 或 支持触摸
  return isMobileUA || isMobileScreen || (hasTouchSupport && screenWidth <= 1024)
}

/**
 * 检测是否为桌面设备
 * @returns {boolean} 如果是桌面设备返回true，否则返回false
 */
export function isDesktopDevice() {
  return !isMobileDevice()
}

/**
 * 获取设备类型字符串
 * @returns {string} 'mobile' 或 'desktop'
 */
export function getDeviceType() {
  return isMobileDevice() ? 'mobile' : 'desktop'
}

/**
 * 检测是否为平板设备
 * @returns {boolean} 如果是平板设备返回true，否则返回false
 */
export function isTabletDevice() {
  const userAgent = navigator.userAgent.toLowerCase()
  const screenWidth = window.innerWidth
  
  // iPad检测
  const isIPad = userAgent.includes('ipad') || 
    (userAgent.includes('macintosh') && navigator.maxTouchPoints > 0)
  
  // Android平板检测
  const isAndroidTablet = userAgent.includes('android') && 
    !userAgent.includes('mobile') && screenWidth >= 768
  
  // 其他平板检测
  const isOtherTablet = (screenWidth >= 768 && screenWidth <= 1024) && 
    navigator.maxTouchPoints > 0
  
  return isIPad || isAndroidTablet || isOtherTablet
}

/**
 * 获取详细的设备信息
 * @returns {object} 包含设备类型、屏幕尺寸、触摸支持等信息的对象
 */
export function getDeviceInfo() {
  return {
    type: getDeviceType(),
    isMobile: isMobileDevice(),
    isDesktop: isDesktopDevice(),
    isTablet: isTabletDevice(),
    screenWidth: window.innerWidth,
    screenHeight: window.innerHeight,
    hasTouchSupport: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
    userAgent: navigator.userAgent,
    pixelRatio: window.devicePixelRatio || 1
  }
}

/**
 * 监听屏幕尺寸变化，动态更新设备类型
 * @param {function} callback 当设备类型改变时的回调函数
 * @returns {function} 取消监听的函数
 */
export function watchDeviceChange(callback) {
  let currentDeviceType = getDeviceType()
  
  const handleResize = () => {
    const newDeviceType = getDeviceType()
    if (newDeviceType !== currentDeviceType) {
      currentDeviceType = newDeviceType
      callback(newDeviceType, getDeviceInfo())
    }
  }
  
  window.addEventListener('resize', handleResize)
  
  // 返回取消监听的函数
  return () => {
    window.removeEventListener('resize', handleResize)
  }
}

/**
 * 根据设备类型获取推荐的详情页路由名称
 * @returns {string} 'DetailMobile' 或 'DetailDesktop'
 */
export function getDetailRouteName() {
  return isMobileDevice() ? 'DetailMobile' : 'DetailDesktop'
}

/**
 * 检测是否支持现代Web特性
 * @returns {object} 包含各种Web特性支持情况的对象
 */
export function getWebFeatureSupport() {
  return {
    webp: (() => {
      const canvas = document.createElement('canvas')
      return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0
    })(),
    intersectionObserver: 'IntersectionObserver' in window,
    resizeObserver: 'ResizeObserver' in window,
    serviceWorker: 'serviceWorker' in navigator,
    localStorage: (() => {
      try {
        localStorage.setItem('test', 'test')
        localStorage.removeItem('test')
        return true
      } catch (e) {
        return false
      }
    })(),
    sessionStorage: (() => {
      try {
        sessionStorage.setItem('test', 'test')
        sessionStorage.removeItem('test')
        return true
      } catch (e) {
        return false
      }
    })()
  }
}

// 导出默认对象，包含所有函数
export default {
  isMobileDevice,
  isDesktopDevice,
  isTabletDevice,
  getDeviceType,
  getDeviceInfo,
  watchDeviceChange,
  getDetailRouteName,
  getWebFeatureSupport
}
