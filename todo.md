# XR图库项目开发计划

## 🎯 项目概述

- **前端**: Naive UI + Vue3 + Pinia + Vite + JavaScript
- **后端**: Go + Hertz + MySQL
- **设计**: 白色渐变色 + 浅黑渐变色主题
- **端口**: Vue(3331) + Go(3332)
- **代理**: /api -> 后端

## 📊 数据库分析

基于 `xr-xrinfo.sql` 的表结构：

### `xr` 表 (主表)

- `id`: 自增主键
- `xrid`: 唯一标识符 (用于rank排序)
- `issave`: 是否保存标记
- `fm`: 封面图片
- `refm`: 参考封面图片 → **封面URL**: `https://re.101616.xyz/` + `refm`
- `title`: 标题
- `url`: 链接地址

### `xrinfo` 表 (图片详情表)

- `id`: 自增主键
- `xrid`: 关联xr表的xrid
- `ourl`: 原始图片URL
- `reurl`: 处理后图片URL → **图片URL**: `https://re.101616.xyz/` + `reurl`

## 🖼️ 图片URL规则

- **封面图片**: `https://img1.101616.xyz/` + `xr.refm`
- **内页图片**: `https://img1.101616.xyz/` + `xrinfo.reurl`
- **代理访问**: `/api/img?url=https://img1.101616.xyz/图片路径`
- **域名限制**: 只允许代理 `https://img1.101616.xyz/` 下的图片

## 🔧 Go后端配置

### 开发环境配置 (config/dev.yaml)

```yaml
server:
  host: "0.0.0.0"
  port: 3332
  mode: "debug"

database:
  host: "localhost"
  port: 3306
  username: "root"
  password: "your_password"
  database: "xr_gallery"
  charset: "utf8mb4"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600

cors:
  allow_origins: ["http://localhost:3331"]
  allow_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
  allow_headers: ["Content-Type", "Authorization"]

jwt:
  secret: "your_dev_jwt_secret_key"
  expire_hours: 24

log:
  level: "debug"
  file_path: "./logs/dev.log"
```

### 生产环境配置 (config/prod.yaml)

```yaml
server:
  host: "0.0.0.0"
  port: 3332
  mode: "release"

database:
  host: "your_prod_db_host"
  port: 3306
  username: "your_prod_username"
  password: "your_prod_password"
  database: "xr_gallery"
  charset: "utf8mb4"
  max_idle_conns: 20
  max_open_conns: 200
  conn_max_lifetime: 3600

cors:
  allow_origins: ["https://your-domain.com"]
  allow_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
  allow_headers: ["Content-Type", "Authorization"]

jwt:
  secret: "your_prod_jwt_secret_key_very_secure"
  expire_hours: 168  # 7天

log:
  level: "info"
  file_path: "./logs/prod.log"
```

## 🚀 API设计

### 1. 首页图库列表

```
GET /api/gallery/list
Query参数:
- page: 页码 (默认1)
- limit: 每页数量 (默认15, 5*3)
- sort: 排序方式 (latest/oldest, 默认latest)
注: latest = xrid DESC (最新), oldest = xrid ASC (最旧)

Response:
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "xrid": 12345,
        "title": "图片标题",
        "cover": "/api/img?url=https://re.101616.xyz/cover123.jpg",
        "cover_original": "cover123.jpg",  // refm字段值
        "image_count": 25,
        "xrid": 12345  // 用于排序，xrid越大越新
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 15,
      "total": 1000,
      "total_pages": 67
    }
  }
}
```

### 2. 图片详情页

```
GET /api/gallery/detail/:xrid
Path参数:
- xrid: 图片集ID

Response:
{
  "code": 200,
  "message": "success",
  "data": {
    "info": {
      "id": 1,
      "xrid": 12345,
      "title": "图片标题",
      "url": "原始链接"
    },
    "images": [
      {
        "id": 1,
        "ourl": "原始图片URL",
        "reurl": "/api/img?url=https://re.101616.xyz/image123.jpg",
        "reurl_original": "image123.jpg",  // reurl字段值
        "order": 1
      }
    ],
    "navigation": {
      "prev": {
        "xrid": 12344,
        "title": "上一套标题",
        "cover": "/api/img?url=https://re.101616.xyz/prev_cover.jpg"
      },
      "next": {
        "xrid": 12346,
        "title": "下一套标题",
        "cover": "/api/img?url=https://re.101616.xyz/next_cover.jpg"
      }
    }
  }
}
```

### 3. 获取相邻图片集 (用于丝滑切换)

```
GET /api/gallery/navigation/:xrid
Path参数:
- xrid: 当前图片集ID

Response:
{
  "code": 200,
  "message": "success",
  "data": {
    "prev": {
      "xrid": 12344,
      "title": "上一套标题",
      "cover": "/api/img?url=https://re.101616.xyz/prev_cover.jpg"
    },
    "next": {
      "xrid": 12346,
      "title": "下一套标题",
      "cover": "/api/img?url=https://re.101616.xyz/next_cover.jpg"
    }
  }
}
```

### 4. 图片代理API (防跨域)

```
GET /api/img
Query参数:
- url: 完整的图片URL (必须是https://re.101616.xyz/开头)

示例:
GET /api/img?url=https://re.101616.xyz/cover123.jpg
GET /api/img?url=https://re.101616.xyz/image456.jpg

Response:
- 直接返回图片二进制数据
- Content-Type: image/jpeg 或对应的图片类型
- 添加适当的缓存头

安全限制:
- 只允许代理 https://re.101616.xyz/ 域名下的图片
- 其他域名返回403 Forbidden
```

### 5. 用户认证 (后期实现)

```
POST /api/auth/login
Body:
{
  "username": "admin",
  "password": "password"
}

Response:
{
  "code": 200,
  "message": "success",
  "data": {
    "token": "jwt_token_here",
    "user": {
      "id": 1,
      "username": "admin"
    }
  }
}
```

```
POST /api/auth/logout
Headers:
Authorization: Bearer jwt_token

Response:
{
  "code": 200,
  "message": "success"
}
```

```
GET /api/auth/profile
Headers:
Authorization: Bearer jwt_token

Response:
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "username": "admin"
  }
}
```

## 🎨 前端Vite配置

### vite.config.js

```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  server: {
    port: 3331,
    proxy: {
      '/api': {
        target: 'http://localhost:3332',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '/api')
      }
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  }
})
```

## 📱 页面设计要求

### 首页 (Gallery List)

- **桌面端**: 5*3网格布局，分页
- **移动端**: 响应式网格，分页
- **主题**: 白色渐变背景
- **组件**: 站头 + 图片网格 + 分页器

### 详情页 (Gallery Detail)

- **桌面端**:
  - 主图居中显示
  - 下方缩略图横向滑动
  - 全部图片一次性加载
- **移动端**:
  - 瀑布流布局，上下滑动
  - 底部上一套/下一套按钮
  - 丝滑切换动画
- **主题**: 浅黑渐变背景
- **加载策略**: 详情页一次性加载所有图片

## 🔄 图片加载策略

### 首页加载

- 封面图片按顺序加载 (第一张加载完再加载第二张)
- 使用图片懒加载优化性能

### 详情页加载

- 进入详情页时一次性请求所有图片数据
- 图片按需渲染，支持预加载
- 失败重试机制

## 📋 开发任务清单 (已通过shrimp-task-manager规划)

### ✅ Phase 1: 基础架构 (并行开发)

- [ ] **任务1**: 项目基础架构搭建 - 初始化前后端项目结构
- [ ] **任务2**: 数据库连接和模型定义 - 建立数据访问层
- [ ] **任务3**: 图片代理API开发 - 实现防跨域图片代理

### ⏳ Phase 2: 核心API开发 (依赖Phase 1)

- [ ] **任务4**: 图库列表API开发 - 首页数据接口
- [ ] **任务5**: 图片详情API开发 - 详情页数据接口

### 🎨 Phase 3: 前端开发 (依赖Phase 2)

- [ ] **任务6**: 前端项目初始化和配置 - Vue3+Naive UI环境
- [ ] **任务7**: 首页图库网格组件开发 - 5×3网格布局
- [ ] **任务8**: 详情页图片查看器开发 - 双端布局适配

### 🚀 Phase 4: 优化完善 (依赖Phase 3)

- [ ] **任务9**: 响应式设计和移动端优化 - 触摸体验优化
- [ ] **任务10**: 性能优化和错误处理 - 整体性能提升

### 🔐 Phase 5: 认证系统 (最后实现)

- [ ] JWT认证中间件
- [ ] 登录页面和API
- [ ] 权限控制机制
- [ ] 用户管理功能

## 🎯 当前开发重点

**正在进行**: Phase 1 - 基础架构搭建
**下一步**: 后端API开发 (图片代理 → 图库列表 → 图片详情)

## 🎨 UI主题设计

### 首页主题 (白色渐变)

```css
background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
```

### 详情页主题 (浅黑渐变)

```css
background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
```

## 📝 注意事项

1. **排序规则**: 使用xrid字段排序，xrid越大越新，首页默认降序显示
2. 详情页只请求一次数据，除非失败重试
3. 移动端需要特别优化触摸体验
4. 图片加载需要错误处理和重试机制
5. **图片代理安全**: 只允许代理 `https://re.101616.xyz/` 域名
6. **URL构建**: 封面用refm字段，内页用reurl字段
7. **导航逻辑**: 上一套=xrid更大(更新), 下一套=xrid更小(更旧)
8. 考虑SEO优化 (后期)

## 🔧 Go后端图片代理实现要点

### 图片代理Handler示例

```go
// /api/img 图片代理接口
func ImageProxyHandler(ctx context.Context, c *app.RequestContext) {
    imageURL := c.Query("url")

    // 安全检查：只允许代理指定域名
    if !strings.HasPrefix(imageURL, "https://re.101616.xyz/") {
        c.JSON(403, gin.H{"error": "Forbidden domain"})
        return
    }

    // 代理请求图片
    resp, err := http.Get(imageURL)
    if err != nil {
        c.JSON(500, gin.H{"error": "Failed to fetch image"})
        return
    }
    defer resp.Body.Close()

    // 设置响应头
    c.Header("Content-Type", resp.Header.Get("Content-Type"))
    c.Header("Cache-Control", "public, max-age=86400") // 缓存1天

    // 返回图片数据
    io.Copy(c.Writer, resp.Body)
}
```

### 数据库查询优化

```go
// 首页列表查询 - 按xrid降序排列 (最新的在前面)
func GetGalleryList(page, limit int, sort string) ([]Gallery, error) {
    var galleries []Gallery

    // 排序逻辑：xrid越大越新，默认降序显示
    orderBy := "ORDER BY xrid DESC"  // 默认最新在前
    if sort == "oldest" {
        orderBy = "ORDER BY xrid ASC"  // 最旧在前
    }

    query := fmt.Sprintf(`
        SELECT id, xrid, title, refm, url,
               (SELECT COUNT(*) FROM xrinfo WHERE xrid = xr.xrid) as image_count
        FROM xr
        WHERE issave = 1
        %s
        LIMIT ? OFFSET ?
    `, orderBy)

    offset := (page - 1) * limit
    err := db.Select(&galleries, query, limit, offset)

    // 查询后构建完整URL
    for i := range galleries {
        if galleries[i].Refm != "" {
            galleries[i].Cover = fmt.Sprintf("/api/img?url=https://re.101616.xyz/%s", galleries[i].Refm)
        }
    }

    return galleries, err
}

// 获取相邻图片集 - 基于xrid排序
func GetNavigationByXrid(currentXrid int) (Navigation, error) {
    var nav Navigation

    // 获取上一套 (xrid更大的，即更新的)
    prevQuery := `
        SELECT xrid, title, refm
        FROM xr
        WHERE xrid > ? AND issave = 1
        ORDER BY xrid ASC
        LIMIT 1
    `

    // 获取下一套 (xrid更小的，即更旧的)
    nextQuery := `
        SELECT xrid, title, refm
        FROM xr
        WHERE xrid < ? AND issave = 1
        ORDER BY xrid DESC
        LIMIT 1
    `

    // 执行查询并构建URL...
    return nav, nil
}
```

## 📈 开发进度

### 已完成 ✅

1. **项目基础架构搭建** - 创建前后端项目结构，配置基础依赖和开发环境
2. **数据库连接和模型定义** - 配置SQLite数据库连接，定义XR和XRInfo数据模型
3. **图片代理服务开发** - 实现图片代理API，支持外部图片访问和缓存
4. **图库列表API开发** - 实现分页、排序的图库列表接口
5. **图片详情API开发** - 实现图库详情、图片列表、导航功能的接口
6. **前端项目初始化和配置** - 完成Vue3+Naive UI+Pinia+Router的完整前端架构配置

### 待完成 📋

7. **首页图库网格组件开发** - 实现5×3响应式网格布局，图库卡片展示
8. **详情页图片查看器开发** - 实现图片浏览器，支持导航和响应式布局
9. **响应式设计和移动端优化** - 完善移动端体验，触摸交互优化
10. **性能优化和错误处理** - 图片懒加载，错误重试，性能监控

### 当前状态 🎯

- **前端服务**: ✅ 运行在 http://localhost:3331
- **后端服务**: ✅ 运行在 http://localhost:3332
- **API连接**: ✅ 前后端通信正常
- **数据库**: ✅ SQLite连接正常，数据完整
- **下一步**: 开发首页图库网格组件
