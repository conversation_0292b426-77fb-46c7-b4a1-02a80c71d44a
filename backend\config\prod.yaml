server:
  host: "0.0.0.0"
  port: 3332
  mode: "release"

database:
  host: "your_prod_db_host"
  port: 3306
  username: "your_prod_username"
  password: "your_prod_password"
  database: "xr_gallery"
  charset: "utf8mb4"
  max_idle_conns: 20
  max_open_conns: 200
  conn_max_lifetime: 3600

cors:
  allow_origins: ["https://your-domain.com"]
  allow_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
  allow_headers: ["Content-Type", "Authorization"]

jwt:
  secret: "your_prod_jwt_secret_key_very_secure"
  expire_hours: 168 # 7天

log:
  level: "info"
  file_path: "./logs/prod.log"

image_proxy:
  allowed_domains: ["https://img1.101616.xyz/"]
  cache_max_age: 86400 # 1天缓存
