package model

// XRInfo 图片详情表模型 - 对应数据库xrinfo表
type XRInfo struct {
	ID    int    `gorm:"primaryKey;autoIncrement" json:"id"`
	XRID  int    `gorm:"index;not null" json:"xrid"`
	OURL  string `gorm:"size:100;uniqueIndex" json:"ourl"`
	ReURL string `gorm:"size:255" json:"reurl"`
}

// TableName 指定表名
func (XRInfo) TableName() string {
	return "xrinfo"
}

// ImageItem 图片项响应结构
type ImageItem struct {
	ID            int    `json:"id"`
	OURL          string `json:"ourl"`
	ReURL         string `json:"reurl"`          // 代理后的图片URL
	ReURLOriginal string `json:"reurl_original"` // 原始reurl字段值
	Order         int    `json:"order"`
}

// GalleryDetail 图库详情响应
type GalleryDetail struct {
	Info       GalleryInfo `json:"info"`
	Images     []ImageItem `json:"images"`
	Navigation Navigation  `json:"navigation"`
}

// GalleryInfo 图库基本信息
type GalleryInfo struct {
	ID    int    `json:"id"`
	XRID  int    `json:"xrid"`
	Title string `json:"title"`
	URL   string `json:"url"`
	Cover string `json:"cover"` // 封面图片代理URL
}
