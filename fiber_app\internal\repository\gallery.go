package repository

import (
	"database/sql"
	"fmt"
	"fiber_app/internal/database"
	"fiber_app/internal/model"
)

// GalleryRepository defines the interface for gallery data operations
type GalleryRepository interface {
	GetGalleryList(page, limit int, sort string) ([]model.XR, int64, error)
	GetGalleryDetail(xrid int) (*model.XR, error)
}

// galleryRepository implements GalleryRepository
type galleryRepository struct {
	db *sql.DB
}

// NewGalleryRepository creates a new GalleryRepository
func NewGalleryRepository() GalleryRepository {
	return &galleryRepository{
		db: database.GetDB(),
	}
}

// GetGalleryList retrieves a list of gallery items with pagination and sorting
func (r *galleryRepository) GetGalleryList(page, limit int, sort string) ([]model.XR, int64, error) {
	offset := (page - 1) * limit

	// Build the base query
	query := "SELECT id, title, description, cover_image_url, created_at, updated_at FROM xr"
	countQuery := "SELECT COUNT(*) FROM xr"

	// Add sorting
	sortOrder := "DESC"
	if sort == "oldest" {
		sortOrder = "ASC"
	}
	query += fmt.Sprintf(" ORDER BY created_at %s", sortOrder)

	// Add pagination
	query += fmt.Sprintf(" LIMIT %d OFFSET %d", limit, offset)

	// Execute count query
	var total int64
	err := r.db.QueryRow(countQuery).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count gallery items: %w", err)
	}

	// Execute main query
	rows, err := r.db.Query(query)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query gallery list: %w", err)
	}
	defer rows.Close()

	var galleries []model.XR
	for rows.Next() {
		var gallery model.XR
		err := rows.Scan(&gallery.ID, &gallery.Title, &gallery.Description, &gallery.CoverImageURL, &gallery.CreatedAt, &gallery.UpdatedAt)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan gallery item: %w", err)
		}
		galleries = append(galleries, gallery)
	}

	if err = rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("rows iteration error: %w", err)
	}

	return galleries, total, nil
}

// GetGalleryDetail retrieves a single gallery item with its associated images
func (r *galleryRepository) GetGalleryDetail(xrid int) (*model.XR, error) {
	var gallery model.XR
	// Query the main gallery item
	row := r.db.QueryRow("SELECT id, title, description, cover_image_url, created_at, updated_at FROM xr WHERE id = ?", xrid)
	err := row.Scan(&gallery.ID, &gallery.Title, &gallery.Description, &gallery.CoverImageURL, &gallery.CreatedAt, &gallery.UpdatedAt)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("gallery with id %d not found", xrid)
		}
		return nil, fmt.Errorf("failed to query gallery detail: %w", err)
	}

	// Query associated images
	rows, err := r.db.Query("SELECT id, xr_id, image_url, description, order_index FROM xr_info WHERE xr_id = ? ORDER BY order_index ASC", xrid)
	if err != nil {
		return nil, fmt.Errorf("failed to query gallery images: %w", err)
	}
	defer rows.Close()

	var images []model.XRInfo
	for rows.Next() {
		var image model.XRInfo
		err := rows.Scan(&image.ID, &image.XRID, &image.ImageURL, &image.Description, &image.OrderIndex)
		if err != nil {
			return nil, fmt.Errorf("failed to scan gallery image: %w", err)
		}
		images = append(images, image)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("rows iteration error for images: %w", err)
	}

	gallery.Images = images

	return &gallery, nil
}
