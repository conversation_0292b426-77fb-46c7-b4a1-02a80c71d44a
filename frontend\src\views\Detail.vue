<template>
  <div class="detail-page">
    <!-- 页面头部 -->
    <header class="detail-header">
      <div class="container">
        <n-button @click="goBack" type="primary" ghost>
          ← 返回首页
        </n-button>
        <div class="header-info" v-if="galleryStore.currentGallery">
          <h1 class="gallery-title">{{ galleryStore.currentGallery.info.title }}</h1>
          <div class="gallery-meta">
            <n-tag type="info">ID: {{ galleryStore.currentGallery.info.xrid }}</n-tag>
            <n-tag type="success">{{ galleryStore.currentGallery.images.length }} 张图片</n-tag>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="detail-content">
      <div class="container">
        <!-- 加载状态 -->
        <Loading v-if="galleryStore.loading" text="加载详情中..." />

        <!-- 错误状态 -->
        <ErrorMessage
          v-else-if="galleryStore.error"
          :title="galleryStore.error"
          description="无法加载图片详情"
          show-back
          @retry="loadDetail"
        />

        <!-- 详情内容 -->
        <div v-else-if="galleryStore.currentGallery" class="gallery-detail">
          <!-- 图片网格 -->
          <div class="images-grid">
            <div
              v-for="(image, index) in galleryStore.currentGallery.images"
              :key="image.id"
              class="image-item"
            >
              <n-image
                :src="image.reurl"
                :alt="`图片 ${image.order}`"
                object-fit="cover"
                :preview-src="image.reurl"
                :img-props="{ loading: 'lazy' }"
                class="gallery-image"
              />
              <div class="image-overlay">
                <span class="image-number">{{ image.order }}</span>
              </div>
            </div>
          </div>

          <!-- 导航区域 -->
          <div class="navigation-section" v-if="hasNavigation">
            <div class="nav-item" v-if="galleryStore.currentGallery.navigation.prev">
              <h3>上一套</h3>
              <n-card
                class="nav-card"
                hoverable
                @click="goToGallery(galleryStore.currentGallery.navigation.prev.xrid)"
              >
                <div class="nav-content">
                  <n-image
                    :src="galleryStore.currentGallery.navigation.prev.cover"
                    :alt="galleryStore.currentGallery.navigation.prev.title"
                    object-fit="cover"
                    preview-disabled
                    class="nav-image"
                  />
                  <div class="nav-info">
                    <h4>{{ galleryStore.currentGallery.navigation.prev.title }}</h4>
                    <n-tag size="small">ID: {{ galleryStore.currentGallery.navigation.prev.xrid }}</n-tag>
                  </div>
                </div>
              </n-card>
            </div>

            <div class="nav-item" v-if="galleryStore.currentGallery.navigation.next">
              <h3>下一套</h3>
              <n-card
                class="nav-card"
                hoverable
                @click="goToGallery(galleryStore.currentGallery.navigation.next.xrid)"
              >
                <div class="nav-content">
                  <n-image
                    :src="galleryStore.currentGallery.navigation.next.cover"
                    :alt="galleryStore.currentGallery.navigation.next.title"
                    object-fit="cover"
                    preview-disabled
                    class="nav-image"
                  />
                  <div class="nav-info">
                    <h4>{{ galleryStore.currentGallery.navigation.next.title }}</h4>
                    <n-tag size="small">ID: {{ galleryStore.currentGallery.navigation.next.xrid }}</n-tag>
                  </div>
                </div>
              </n-card>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { useGalleryStore } from '@/stores/gallery'
import Loading from '@/components/Loading.vue'
import ErrorMessage from '@/components/ErrorMessage.vue'

const route = useRoute()
const router = useRouter()
const message = useMessage()
const galleryStore = useGalleryStore()

// 计算属性
const hasNavigation = computed(() => {
  const nav = galleryStore.currentGallery?.navigation
  return nav && (nav.prev || nav.next)
})

// 加载详情
async function loadDetail() {
  const xrid = parseInt(route.params.xrid)
  if (!xrid) {
    message.error('无效的图库ID')
    return
  }

  try {
    await galleryStore.fetchGalleryDetail(xrid)
  } catch (error) {
    message.error('加载详情失败')
  }
}

// 返回首页
function goBack() {
  router.push({ name: 'Home' })
}

// 跳转到其他图库
function goToGallery(xrid) {
  router.push({ name: 'Detail', params: { xrid } })
}

// 监听路由变化
watch(() => route.params.xrid, () => {
  if (route.name === 'Detail') {
    loadDetail()
  }
})

// 组件挂载时加载数据
onMounted(() => {
  loadDetail()
})
</script>

<style scoped>
.detail-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
  color: white;
}

.detail-header {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: var(--spacing-lg) 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.detail-header .container {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.header-info {
  flex: 1;
}

.gallery-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-sm) 0;
  line-height: 1.4;
}

.gallery-meta {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.detail-content {
  padding: var(--spacing-xl) 0;
}

/* 移动端内容区域 */
@media (max-width: 768px) {
  .detail-content {
    padding: var(--spacing-lg) 0;
  }

  .detail-content .container {
    padding: 0;
    max-width: 100%;
  }
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-2xl);
}

/* 移动端1×1布局 */
@media (max-width: 768px) {
  .images-grid {
    grid-template-columns: 1fr;
    gap: 0;
    margin-bottom: var(--spacing-xl);
  }
}

.image-item {
  position: relative;
  aspect-ratio: 3/4;
  border-radius: var(--border-radius);
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
}

/* 移动端图片样式 */
@media (max-width: 768px) {
  .image-item {
    aspect-ratio: auto;
    border-radius: 0;
    margin-bottom: var(--spacing-md);
    background: transparent;
    overflow: visible;
  }
}

.gallery-image {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

/* 移动端图片样式 - 占满屏幕宽度 */
@media (max-width: 768px) {
  .gallery-image {
    width: 100vw;
    height: auto;
    object-fit: contain;
    margin-left: calc(-50vw + 50%);
    max-width: none;
  }

  .image-item:hover .gallery-image {
    transform: none; /* 移动端禁用悬停缩放 */
  }
}

.image-item:hover .gallery-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

/* 移动端图片序号 */
@media (max-width: 768px) {
  .image-overlay {
    top: var(--spacing-xs);
    right: var(--spacing-xs);
    font-size: 0.7rem;
    padding: 2px 6px;
  }
}

.navigation-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-2xl);
  padding-top: var(--spacing-xl);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-item h3 {
  color: white;
  margin-bottom: var(--spacing-md);
  font-size: 1.2rem;
}

.nav-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-card:hover {
  transform: translateY(-4px);
}

.nav-content {
  display: flex;
  gap: var(--spacing-md);
}

.nav-image {
  width: 80px;
  height: 100px;
  border-radius: var(--border-radius);
  flex-shrink: 0;
}

.nav-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.nav-info h4 {
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .detail-header .container {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  .gallery-title {
    font-size: 1.2rem;
  }

  .images-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-sm);
  }

  .navigation-section {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
}

@media (max-width: 480px) {
  .images-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .nav-content {
    flex-direction: column;
    text-align: center;
  }

  .nav-image {
    width: 100%;
    height: 120px;
  }
}
</style>
