package handler

import (
	"log"
	"strconv"
	"fiber_app/internal/model"
	"fiber_app/internal/repository"

	"github.com/gofiber/fiber/v2"
)

// GetGalleryList 获取图库列表
func GetGalleryList(c *fiber.Ctx) error {
	// 获取查询参数
	pageStr := c.Query("page", "1")
	limitStr := c.Query("limit", "15")
	sort := c.Query("sort", "latest")

	// 参数验证和转换
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		return c.Status(fiber.StatusBadRequest).JSON(model.Response{
			Code:    fiber.StatusBadRequest,
			Message: "Invalid page parameter",
		})
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		return c.Status(fiber.StatusBadRequest).JSON(model.Response{
			Code:    fiber.StatusBadRequest,
			Message: "Invalid limit parameter (1-100)",
		})
	}

	// 验证排序参数
	if sort != "latest" && sort != "oldest" {
		return c.Status(fiber.StatusBadRequest).JSON(model.Response{
			Code:    fiber.StatusBadRequest,
			Message: "Invalid sort parameter (latest/oldest)",
		})
	}

	// 创建仓库实例
	repo := repository.NewGalleryRepository()

	// 查询图库列表
	galleries, total, err := repo.GetGalleryList(page, limit, sort)
	if err != nil {
		log.Printf("Failed to get gallery list: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(model.Response{
			Code:    fiber.StatusInternalServerError,
			Message: "Internal server error",
		})
	}

	// 计算分页信息
	totalPages := int((total + int64(limit) - 1) / int64(limit))

	// 构建响应
	response := model.GalleryListResponse{
		List: galleries,
		Pagination: model.Pagination{
			CurrentPage: page,
			PerPage:     limit,
			Total:       int(total),
			TotalPages:  totalPages,
		},
	}

	// 返回成功响应
	log.Printf("Gallery list retrieved: page=%d, limit=%d, sort=%s, total=%d", page, limit, sort, total)
	return c.JSON(model.Response{
		Code:    fiber.StatusOK,
		Message: "success",
		Data:    response,
	})
}

// GetGalleryDetail 获取图库详情
func GetGalleryDetail(c *fiber.Ctx) error {
	// 获取路径参数
	xridStr := c.Params("xrid")
	if xridStr == "" {
		return c.Status(fiber.StatusBadRequest).JSON(model.Response{
			Code:    fiber.StatusBadRequest,
			Message: "Missing xrid parameter",
		})
	}

	// 参数验证和转换
	xrid, err := strconv.Atoi(xridStr)
	if err != nil || xrid <= 0 {
		return c.Status(fiber.StatusBadRequest).JSON(model.Response{
			Code:    fiber.StatusBadRequest,
			Message: "Invalid xrid parameter",
		})
	}

	// 创建仓库实例
	repo := repository.NewGalleryRepository()

	// 查询图库详情
	detail, err := repo.GetGalleryDetail(xrid)
	if err != nil {
		log.Printf("Failed to get gallery detail for xrid %d: %v", xrid, err)
		return c.Status(fiber.StatusNotFound).JSON(model.Response{
			Code:    fiber.StatusNotFound,
			Message: "Gallery not found",
		})
	}

	// 返回成功响应
	log.Printf("Gallery detail retrieved: xrid=%d, images=%d", xrid, len(detail.Images))
	return c.JSON(model.Response{
		Code:    fiber.StatusOK,
		Message: "success",
		Data:    detail,
	})
}
