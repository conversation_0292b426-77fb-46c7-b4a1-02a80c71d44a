package service

import (
	"fmt"
	"xr-gallery/internal/model"
	"xr-gallery/internal/repository"
)

type GalleryService struct {
	repo *repository.GalleryRepository
}

// NewGalleryService 创建图库服务实例
func NewGalleryService() *GalleryService {
	return &GalleryService{
		repo: repository.NewGalleryRepository(),
	}
}

// GetGalleryList 获取图库列表（业务逻辑层）
func (s *GalleryService) GetGalleryList(page, limit int, sort string) (*model.GalleryListResponse, error) {
	// 参数验证
	if page < 1 {
		return nil, fmt.Errorf("page must be greater than 0")
	}
	if limit < 1 || limit > 100 {
		return nil, fmt.Errorf("limit must be between 1 and 100")
	}
	if sort != "latest" && sort != "oldest" {
		return nil, fmt.Errorf("sort must be 'latest' or 'oldest'")
	}

	// 调用仓库层获取数据
	galleries, total, err := s.repo.GetGalleryList(page, limit, sort)
	if err != nil {
		return nil, fmt.Errorf("failed to get gallery list: %w", err)
	}

	// 计算分页信息
	totalPages := int((total + int64(limit) - 1) / int64(limit))

	// 构建响应
	response := &model.GalleryListResponse{
		List: galleries,
		Pagination: model.Pagination{
			CurrentPage: page,
			PerPage:     limit,
			Total:       int(total),
			TotalPages:  totalPages,
		},
	}

	return response, nil
}

// GetGalleryDetail 获取图库详情（业务逻辑层）
func (s *GalleryService) GetGalleryDetail(xrid int) (*model.GalleryDetail, error) {
	if xrid <= 0 {
		return nil, fmt.Errorf("xrid must be greater than 0")
	}

	// 调用仓库层获取数据
	detail, err := s.repo.GetGalleryDetail(xrid)
	if err != nil {
		return nil, fmt.Errorf("failed to get gallery detail: %w", err)
	}

	return detail, nil
}

// GetNavigation 获取导航信息（业务逻辑层）
func (s *GalleryService) GetNavigation(xrid int) (*model.Navigation, error) {
	if xrid <= 0 {
		return nil, fmt.Errorf("xrid must be greater than 0")
	}

	// 调用仓库层获取数据
	navigation, err := s.repo.GetNavigation(xrid)
	if err != nil {
		return nil, fmt.Errorf("failed to get navigation: %w", err)
	}

	return navigation, nil
}
