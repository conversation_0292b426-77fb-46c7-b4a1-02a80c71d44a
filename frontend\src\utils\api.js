// API基础配置
const API_BASE_URL = '/api'

// 通用请求函数
async function request(url, options = {}) {
  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  }

  try {
    const response = await fetch(`${API_BASE_URL}${url}`, config)
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    
    // 检查业务状态码
    if (data.code !== 200) {
      throw new Error(data.message || '请求失败')
    }
    
    return data
  } catch (error) {
    console.error('API请求错误:', error)
    throw error
  }
}

// GET请求
export function get(url, params = {}) {
  const searchParams = new URLSearchParams(params)
  const queryString = searchParams.toString()
  const fullUrl = queryString ? `${url}?${queryString}` : url
  
  return request(fullUrl, {
    method: 'GET'
  })
}

// POST请求
export function post(url, data = {}) {
  return request(url, {
    method: 'POST',
    body: JSON.stringify(data)
  })
}

// 图库API
export const galleryApi = {
  // 获取图库列表
  getList(params = {}) {
    return get('/gallery/list', params)
  },
  
  // 获取图库详情
  getDetail(xrid) {
    return get(`/gallery/detail/${xrid}`)
  }
}

// 测试API连接
export function testConnection() {
  return get('/ping')
}

// 图片代理URL构建
export function getImageUrl(originalUrl) {
  if (!originalUrl) return ''
  
  // 如果已经是代理URL，直接返回
  if (originalUrl.startsWith('/api/img')) {
    return originalUrl
  }
  
  // 构建代理URL
  return `/api/img?url=${encodeURIComponent(originalUrl)}`
}
