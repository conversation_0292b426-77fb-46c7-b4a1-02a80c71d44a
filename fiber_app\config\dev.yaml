database:
  driver: sqlite3
  dsn: ./data.db
  host: ""
  port: 0
  username: ""
  password: ""
  database: ""
  charset: ""
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600
server:
  host: "0.0.0.0"
  port: 8080
  mode: "debug"
cors:
  allow_origins: ["http://localhost:3331"]
  allow_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
  allow_headers: ["Content-Type", "Authorization"]
jwt:
  secret: "your_dev_jwt_secret_key"
  expire_hours: 24
log:
  level: "debug"
  file_path: "./logs/dev.log"
image_proxy:
  allowed_domains: ["https://img1.101616.xyz/"]
  cache_max_age: 86400 # 1天缓存
