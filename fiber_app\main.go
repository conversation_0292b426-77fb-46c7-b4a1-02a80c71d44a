package main

import (
	"fmt"
	"log"
	"strings"
	"fiber_app/internal/config"
	"fiber_app/internal/database"
	"fiber_app/internal/handler"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	_ "github.com/mattn/go-sqlite3" // SQLite driver
)

func main() {
	// Load configuration
	cfg, err := config.LoadConfig(config.GetConfigPath())
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize database connection
	if err := database.InitDB(cfg.Database); err != nil {
		log.Fatalf("Error initializing database: %v", err)
	}
	defer database.CloseDB()

	// Create Fiber app
	app := fiber.New()

	// Add CORS middleware
	app.Use(cors.New(cors.Config{
		AllowOrigins: strings.Join(cfg.CORS.AllowOrigins, ","),
		AllowMethods: strings.Join(cfg.CORS.AllowMethods, ","),
		AllowHeaders: strings.Join(cfg.CORS.AllowHeaders, ","),
	}))

	// Routes
	app.Get("/", func(c *fiber.Ctx) error {
		return c.SendString("Hello, Fiber with database/sql!")
	})

	// Image proxy route
	app.Get("/api/img", handler.ImageProxyHandler(cfg))

	// Gallery API routes
	app.Get("/api/gallery/list", handler.GetGalleryList)
	app.Get("/api/gallery/detail/:xrid", handler.GetGalleryDetail)

	// Start server
	log.Printf("Server listening on %s:%d", cfg.Server.Host, cfg.Server.Port)
	log.Fatal(app.Listen(fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port)))
}
