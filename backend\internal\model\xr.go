package model

// XR 主表模型 - 对应数据库xr表
type XR struct {
	ID     int    `gorm:"primaryKey;autoIncrement" json:"id"`
	XRID   int    `gorm:"uniqueIndex;not null" json:"xrid"`
	IsSave int    `gorm:"default:0" json:"issave"`
	FM     string `gorm:"size:255" json:"fm"`
	ReFM   string `gorm:"size:255" json:"refm"`
	Title  string `gorm:"size:255" json:"title"`
	URL    string `gorm:"size:255" json:"url"`
}

// TableName 指定表名
func (XR) TableName() string {
	return "xr"
}

// GalleryListItem 图库列表项响应结构
type GalleryListItem struct {
	ID            int    `json:"id"`
	XRID          int    `json:"xrid"`
	Title         string `json:"title"`
	Cover         string `json:"cover"`          // 代理后的封面URL
	CoverOriginal string `json:"cover_original"` // 原始refm字段值
	ImageCount    int    `json:"image_count"`
}

// GalleryListResponse 图库列表响应
type GalleryListResponse struct {
	List       []GalleryListItem `json:"list"`
	Pagination Pagination        `json:"pagination"`
}

// Pagination 分页信息
type Pagination struct {
	CurrentPage int `json:"current_page"`
	PerPage     int `json:"per_page"`
	Total       int `json:"total"`
	TotalPages  int `json:"total_pages"`
}
