# 🚀 Fiber 后端应用

欢迎来到 `fiber_app`！这是一个使用 Go 语言的 [Fiber](https://gofiber.io/) 框架构建的后端应用程序，旨在提供高性能的 API 服务。它集成了 `database/sql` 进行数据库操作，并支持灵活的配置管理。

## ✨ 主要特性

-   ⚡ **高性能**: 基于 Fiber 框架，提供极速的 HTTP 路由。
-   🔌 **数据库集成**: 使用 Go 标准库 `database/sql` 进行高效、灵活的数据库交互。
-   ⚙️ **灵活配置**: 通过 YAML 文件管理应用配置，支持开发和生产环境分离。
-   🖼️ **图片代理**: 提供图片代理服务，解决跨域问题并支持缓存。
-   📊 **图库管理**: 提供图库列表和详情 API，支持分页和排序。
-   🔒 **CORS 支持**: 内置 CORS 中间件，轻松处理跨域请求。

## 🛠️ 技术栈

-   **后端框架**: [Fiber v2](https://gofiber.io/)
-   **数据库驱动**: 
    -   [go-sqlite3](https://github.com/mattn/go-sqlite3) (默认用于开发环境)
    -   [go-sql-driver/mysql](https://github.com/go-sql-driver/mysql) (支持 MySQL)
-   **配置管理**: [gopkg.in/yaml.v3](https://gopkg.in/yaml.v3)

## 📦 安装与运行

### 前提条件

在开始之前，请确保您的系统已安装以下软件：

-   [Go 1.16+](https://golang.org/dl/)

### 安装依赖

进入项目根目录，运行以下命令安装所有 Go 模块依赖：

```bash
cd fiber_app
go mod tidy
```

### 启动应用

您可以通过以下命令启动应用：

```bash
cd fiber_app
go run main.go
```

默认情况下，应用将在 `http://localhost:8080` 启动。您可以通过修改 `config/dev.yaml` 或设置 `GO_ENV` 环境变量来切换配置。

#### 环境变量

-   `GO_ENV`: 设置为 `production` 将加载 `config/prod.yaml`，否则加载 `config/dev.yaml`。

    **示例 (Linux/macOS):**
    ```bash
    GO_ENV=production go run main.go
    ```

    **示例 (Windows CMD):**
    ```cmd
    set GO_ENV=production
    go run main.go
    ```

## 🌐 API 端点

以下是本应用提供的主要 API 端点：

-   **`GET /`**
    -   **描述**: 简单的健康检查或欢迎信息。
    -   **响应**: `Hello, Fiber with database/sql!`

-   **`GET /api/img`**
    -   **描述**: 图片代理服务，用于解决图片跨域问题。
    -   **参数**: 
        -   `url` (查询参数): 待代理的图片 URL。
    -   **示例**: `http://localhost:8080/api/img?url=https://img1.101616.xyz/your-image.jpg`

-   **`GET /api/gallery/list`**
    -   **描述**: 获取图库列表，支持分页和排序。
    -   **参数**: 
        -   `page` (查询参数, 可选): 当前页码，默认为 `1`。
        -   `limit` (查询参数, 可选): 每页数量，默认为 `15`，最大 `100`。
        -   `sort` (查询参数, 可选): 排序方式，`latest` (最新) 或 `oldest` (最旧)，默认为 `latest`。
    -   **示例**: `http://localhost:8080/api/gallery/list?page=1&limit=10&sort=oldest`

-   **`GET /api/gallery/detail/:xrid`**
    -   **描述**: 获取指定 ID 的图库详情，包含所有相关图片。
    -   **参数**: 
        -   `xrid` (路径参数): 图库的唯一 ID。
    -   **示例**: `http://localhost:8080/api/gallery/detail/123`

## ⚙️ 配置说明

应用配置通过 `config/dev.yaml` (开发环境) 和 `config/prod.yaml` (生产环境) 文件管理。主要配置项包括：

```yaml
server:
  host: "0.0.0.0" # 服务器监听地址
  port: 8080      # 服务器监听端口
  mode: "debug"   # 运行模式 (debug/release)

database:
  driver: sqlite3 # 数据库驱动 (sqlite3 或 mysql)
  dsn: ./data.db  # SQLite 数据库文件路径或 MySQL DSN (如果使用 MySQL，此项可留空)
  host: ""        # MySQL 主机地址
  port: 0         # MySQL 端口
  username: ""    # MySQL 用户名
  password: ""    # MySQL 密码
  database: ""    # MySQL 数据库名
  charset: ""     # MySQL 字符集
  max_idle_conns: 10 # 数据库连接池最大空闲连接数
  max_open_conns: 100 # 数据库连接池最大打开连接数
  conn_max_lifetime: 3600 # 连接可复用的最大时间 (秒)

cors:
  allow_origins: ["http://localhost:3331"] # 允许跨域的源列表
  allow_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"] # 允许的 HTTP 方法
  allow_headers: ["Content-Type", "Authorization"] # 允许的请求头

jwt:
  secret: "your_dev_jwt_secret_key" # JWT 密钥
  expire_hours: 24                  # JWT 过期时间 (小时)

log:
  level: "debug"    # 日志级别 (debug/info/warn/error)
  file_path: "./logs/dev.log" # 日志文件路径

image_proxy:
  allowed_domains: ["https://img1.101616.xyz/"] # 允许代理的图片域名列表
  cache_max_age: 86400 # 图片代理缓存时间 (秒)
```

## 🗄️ 数据库

本应用默认使用 SQLite 数据库，数据库文件 `data.db` 将在应用首次启动时自动创建在 `fiber_app` 目录下。如果您想使用 MySQL，请修改 `config/dev.yaml` (或 `prod.yaml`) 中的 `database` 配置。

**注意**: 数据库表 (`xr` 和 `xr_info`) 的创建逻辑已内置在 `internal/database/connection.go` 的 `InitDB` 函数中，会在应用启动时自动执行。

## 🤝 贡献

欢迎任何形式的贡献！如果您有任何建议、Bug 报告或功能请求，请随时提交 Issue 或 Pull Request。

---