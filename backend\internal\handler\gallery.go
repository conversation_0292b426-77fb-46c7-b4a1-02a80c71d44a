package handler

import (
	"context"
	"log"
	"strconv"
	"xr-gallery/internal/model"
	"xr-gallery/internal/repository"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/common/utils"
)

// GetGalleryList 获取图库列表
func GetGalleryList(ctx context.Context, c *app.RequestContext) {
	// 获取查询参数
	pageStr := c.<PERSON>fault<PERSON><PERSON>y("page", "1")
	limitStr := c.<PERSON>fault<PERSON>("limit", "15")
	sort := c.<PERSON>fault<PERSON>uery("sort", "latest")

	// 参数验证和转换
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		c.<PERSON><PERSON>("Content-Type", "application/json; charset=utf-8")
		c.<PERSON><PERSON>(400, utils.H{
			"code":    400,
			"message": "Invalid page parameter",
		})
		return
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		c.<PERSON><PERSON>("Content-Type", "application/json; charset=utf-8")
		c.<PERSON>(400, utils.H{
			"code":    400,
			"message": "Invalid limit parameter (1-100)",
		})
		return
	}

	// 验证排序参数
	if sort != "latest" && sort != "oldest" {
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(400, utils.H{
			"code":    400,
			"message": "Invalid sort parameter (latest/oldest)",
		})
		return
	}

	// 创建仓库实例
	repo := repository.NewGalleryRepository()

	// 查询图库列表
	galleries, total, err := repo.GetGalleryList(page, limit, sort)
	if err != nil {
		log.Printf("Failed to get gallery list: %v", err)
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(500, utils.H{
			"code":    500,
			"message": "Internal server error",
		})
		return
	}

	// 计算分页信息
	totalPages := int((total + int64(limit) - 1) / int64(limit))

	// 构建响应
	response := model.GalleryListResponse{
		List: galleries,
		Pagination: model.Pagination{
			CurrentPage: page,
			PerPage:     limit,
			Total:       int(total),
			TotalPages:  totalPages,
		},
	}

	// 返回成功响应
	c.Header("Content-Type", "application/json; charset=utf-8")
	c.JSON(200, utils.H{
		"code":    200,
		"message": "success",
		"data":    response,
	})

	log.Printf("Gallery list retrieved: page=%d, limit=%d, sort=%s, total=%d", page, limit, sort, total)
}

// GetGalleryDetail 获取图库详情
func GetGalleryDetail(ctx context.Context, c *app.RequestContext) {
	// 获取路径参数
	xridStr := c.Param("xrid")
	if xridStr == "" {
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(400, utils.H{
			"code":    400,
			"message": "Missing xrid parameter",
		})
		return
	}

	// 参数验证和转换
	xrid, err := strconv.Atoi(xridStr)
	if err != nil || xrid <= 0 {
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(400, utils.H{
			"code":    400,
			"message": "Invalid xrid parameter",
		})
		return
	}

	// 创建仓库实例
	repo := repository.NewGalleryRepository()

	// 查询图库详情
	detail, err := repo.GetGalleryDetail(xrid)
	if err != nil {
		log.Printf("Failed to get gallery detail for xrid %d: %v", xrid, err)
		c.Header("Content-Type", "application/json; charset=utf-8")
		c.JSON(404, utils.H{
			"code":    404,
			"message": "Gallery not found",
		})
		return
	}

	// 返回成功响应
	c.Header("Content-Type", "application/json; charset=utf-8")
	c.JSON(200, utils.H{
		"code":    200,
		"message": "success",
		"data":    detail,
	})

	log.Printf("Gallery detail retrieved: xrid=%d, images=%d", xrid, len(detail.Images))
}
