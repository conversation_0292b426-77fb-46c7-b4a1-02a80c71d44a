package handler

import (
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"
	"fiber_app/internal/config"

	"github.com/gofiber/fiber/v2"
)

// ImageProxyHandler 图片代理处理器
// 解决前端访问图片的跨域问题
func ImageProxyHandler(cfg *config.Config) fiber.Handler {
	return func(c *fiber.Ctx) error {
		imageURL := c.Query("url")

		// 参数验证
		if imageURL == "" {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"code":    fiber.StatusBadRequest,
				"message": "Missing url parameter",
			})
		}

		// 安全检查：只允许代理指定域名
		allowed := false
		for _, domain := range cfg.ImageProxy.AllowedDomains {
			if strings.HasPrefix(imageURL, domain) {
				allowed = true
				break
			}
		}

		if !allowed {
			log.Printf("Forbidden domain access attempt: %s", imageURL)
			return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
				"code":    fiber.StatusForbidden,
				"message": "Forbidden domain",
			})
		}

		// 创建HTTP客户端，设置超时
		client := &http.Client{
			Timeout: 30 * time.Second,
		}

		// 代理请求图片
		resp, err := client.Get(imageURL)
		if err != nil {
			log.Printf("Failed to fetch image %s: %v", imageURL, err)
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"code":    fiber.StatusInternalServerError,
				"message": "Failed to fetch image",
			})
		}
		defer resp.Body.Close()

		// 检查响应状态
		if resp.StatusCode != http.StatusOK {
			log.Printf("Image request failed with status %d for URL: %s", resp.StatusCode, imageURL)
			return c.Status(resp.StatusCode).JSON(fiber.Map{
				"code":    resp.StatusCode,
				"message": fmt.Sprintf("Image request failed: %s", resp.Status),
			})
		}

		// 设置响应头
		contentType := resp.Header.Get("Content-Type")
		if contentType == "" {
			contentType = "image/jpeg" // 默认类型
		}

		c.Set("Content-Type", contentType)
		c.Set("Cache-Control", fmt.Sprintf("public, max-age=%d", cfg.ImageProxy.CacheMaxAge))
		c.Set("Access-Control-Allow-Origin", strings.Join(cfg.CORS.AllowOrigins, ",")) // Use configured origins
		c.Set("Access-Control-Allow-Methods", strings.Join(cfg.CORS.AllowMethods, ","))
		c.Set("Access-Control-Allow-Headers", strings.Join(cfg.CORS.AllowHeaders, ","))

		// 如果有Content-Length，也设置上
		if contentLength := resp.Header.Get("Content-Length"); contentLength != "" {
			c.Set("Content-Length", contentLength)
		}

		// 返回图片数据
		return c.SendStream(resp.Body)
	}
}
