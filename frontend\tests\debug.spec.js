import { test, expect } from '@playwright/test';

test.describe('XR Gallery Debug Tests', () => {
  test('Check homepage loading and image display', async ({ page }) => {
    // 导航到首页
    await page.goto('/');

    // 等待页面加载
    await page.waitForLoadState('networkidle');

    // 检查页面标题
    await expect(page).toHaveTitle(/XR Gallery/);

    // 检查是否有图库卡片
    const galleryCards = page.locator('.gallery-item');
    await expect(galleryCards.first()).toBeVisible({ timeout: 10000 });

    // 检查图片加载状态
    const firstImage = page.locator('.gallery-image').first();
    const firstPlaceholder = page.locator('.image-placeholder').first();
    const firstLoadingOverlay = page.locator('.loading-overlay').first();

    console.log('Image visible:', await firstImage.isVisible());
    console.log('Placeholder visible:', await firstPlaceholder.isVisible());
    console.log('Loading overlay visible:', await firstLoadingOverlay.isVisible());

    // 等待图片加载完成或超时
    try {
      await firstImage.waitFor({ state: 'visible', timeout: 15000 });
      console.log('Image loaded successfully');
    } catch (error) {
      console.log('Image loading timeout:', error.message);
    }

    // 截图保存
    await page.screenshot({ path: 'tests/screenshots/homepage-images.png', fullPage: true });
  });
  
  test('Check API response encoding', async ({ page }) => {
    // 监听网络请求
    const apiResponses = [];
    
    page.on('response', async (response) => {
      if (response.url().includes('/api/gallery/list')) {
        const contentType = response.headers()['content-type'];
        const responseText = await response.text();
        
        apiResponses.push({
          url: response.url(),
          status: response.status(),
          contentType,
          bodyPreview: responseText.substring(0, 200)
        });
        
        console.log('API Response:', {
          url: response.url(),
          status: response.status(),
          contentType,
          bodyPreview: responseText.substring(0, 200)
        });
      }
    });
    
    // 导航到首页触发API请求
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // 验证API响应
    expect(apiResponses.length).toBeGreaterThan(0);
    
    const apiResponse = apiResponses[0];
    expect(apiResponse.status).toBe(200);
    expect(apiResponse.contentType).toContain('application/json');
    expect(apiResponse.contentType).toContain('charset=utf-8');
  });
  
  test('Test device detection and routing', async ({ page }) => {
    // 导航到首页
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // 点击第一个图库卡片
    const firstCard = page.locator('.gallery-item').first();
    await expect(firstCard).toBeVisible({ timeout: 10000 });

    // 监听控制台日志，查看设备检测结果
    const consoleMessages = [];
    page.on('console', msg => {
      if (msg.text().includes('设备类型检测')) {
        consoleMessages.push(msg.text());
      }
    });

    await firstCard.click();

    // 等待详情页加载
    await page.waitForLoadState('networkidle');

    // 检查URL是否包含detail
    expect(page.url()).toContain('/detail/');

    // 检查设备检测日志
    console.log('设备检测日志:', consoleMessages);

    // 截图保存
    await page.screenshot({ path: 'tests/screenshots/detail-page.png', fullPage: true });
  });

  test('Test mobile waterfall layout', async ({ page }) => {
    // 模拟移动设备
    await page.setViewportSize({ width: 375, height: 667 });

    // 直接导航到移动端详情页
    await page.goto('/mobile/detail/17735');
    await page.waitForLoadState('networkidle');

    // 检查瀑布流容器
    const waterfallContainer = page.locator('.waterfall-container');
    await expect(waterfallContainer).toBeVisible({ timeout: 10000 });

    // 检查图片宽度是否等于屏幕宽度
    const firstImage = page.locator('.waterfall-image').first();
    await expect(firstImage).toBeVisible({ timeout: 10000 });

    // 获取图片和屏幕宽度
    const imageWidth = await firstImage.evaluate(img => img.offsetWidth);
    const screenWidth = await page.evaluate(() => window.innerWidth);

    console.log(`图片宽度: ${imageWidth}px, 屏幕宽度: ${screenWidth}px`);

    // 验证图片宽度等于屏幕宽度
    expect(imageWidth).toBe(screenWidth);

    // 检查页面是否禁止水平滚动
    const bodyOverflowX = await page.evaluate(() =>
      window.getComputedStyle(document.body).overflowX
    );
    console.log(`Body overflow-x: ${bodyOverflowX}`);

    // 截图保存
    await page.screenshot({ path: 'tests/screenshots/mobile-waterfall.png', fullPage: true });
  });

  test('Test desktop main image height fitting', async ({ page }) => {
    // 设置桌面端视口
    await page.setViewportSize({ width: 1280, height: 720 });

    // 直接导航到桌面端详情页
    await page.goto('/desktop/detail/17735');
    await page.waitForLoadState('networkidle');

    // 检查主图容器
    const mainImageContainer = page.locator('.main-image-container');
    await expect(mainImageContainer).toBeVisible({ timeout: 10000 });

    // 检查主图
    const mainImage = page.locator('.main-image');
    await expect(mainImage).toBeVisible({ timeout: 10000 });

    // 获取容器和图片的尺寸
    const containerHeight = await mainImageContainer.evaluate(el => el.offsetHeight);
    const imageHeight = await mainImage.evaluate(img => img.offsetHeight);
    const imageWidth = await mainImage.evaluate(img => img.offsetWidth);

    console.log(`容器高度: ${containerHeight}px`);
    console.log(`图片高度: ${imageHeight}px`);
    console.log(`图片宽度: ${imageWidth}px`);

    // 验证图片高度等于容器高度（允许1px的误差）
    expect(Math.abs(imageHeight - containerHeight)).toBeLessThanOrEqual(1);

    // 验证容器高度应该更高（约75vh）
    const viewportHeight = await page.evaluate(() => window.innerHeight);
    const expectedHeight = Math.floor(viewportHeight * 0.75);
    console.log(`视口高度: ${viewportHeight}px, 期望容器高度: ${expectedHeight}px`);
    expect(Math.abs(containerHeight - expectedHeight)).toBeLessThanOrEqual(5);

    // 截图保存
    await page.screenshot({ path: 'tests/screenshots/desktop-main-image.png', fullPage: true });
  });
});
