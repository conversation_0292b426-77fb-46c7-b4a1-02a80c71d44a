<template>
  <div class="error-container">
    <n-result
      status="error"
      :title="title"
      :description="description"
    >
      <template #footer>
        <n-button 
          v-if="showRetry" 
          type="primary" 
          @click="$emit('retry')"
        >
          重试
        </n-button>
        <n-button 
          v-if="showBack" 
          @click="goBack"
          style="margin-left: 12px;"
        >
          返回
        </n-button>
      </template>
    </n-result>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

defineProps({
  title: {
    type: String,
    default: '出错了'
  },
  description: {
    type: String,
    default: '请稍后重试'
  },
  showRetry: {
    type: Boolean,
    default: true
  },
  showBack: {
    type: Boolean,
    default: false
  }
})

defineEmits(['retry'])

function goBack() {
  router.back()
}
</script>

<style scoped>
.error-container {
  padding: 40px 20px;
  text-align: center;
}
</style>
