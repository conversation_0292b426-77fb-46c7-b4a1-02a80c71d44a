package handler

import (
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/common/utils"
)

// ImageProxyHandler 图片代理处理器
// 解决前端访问https://re.101616.xyz/图片的跨域问题
func ImageProxyHandler(ctx context.Context, c *app.RequestContext) {
	imageURL := c.Query("url")

	// 参数验证
	if imageURL == "" {
		c.JSON(400, utils.H{
			"code":    400,
			"message": "Missing url parameter",
		})
		return
	}

	// 安全检查：只允许代理指定域名
	if !strings.HasPrefix(imageURL, "https://img1.101616.xyz/") {
		log.Printf("Forbidden domain access attempt: %s", imageURL)
		c.JSON(403, utils.H{
			"code":    403,
			"message": "Forbidden domain",
		})
		return
	}

	// 创建HTTP客户端，设置超时
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 代理请求图片
	resp, err := client.Get(imageURL)
	if err != nil {
		log.Printf("Failed to fetch image %s: %v", imageURL, err)
		c.JSON(500, utils.H{
			"code":    500,
			"message": "Failed to fetch image",
		})
		return
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		log.Printf("Image request failed with status %d for URL: %s", resp.StatusCode, imageURL)
		c.JSON(resp.StatusCode, utils.H{
			"code":    resp.StatusCode,
			"message": fmt.Sprintf("Image request failed: %s", resp.Status),
		})
		return
	}

	// 设置响应头
	contentType := resp.Header.Get("Content-Type")
	if contentType == "" {
		contentType = "image/jpeg" // 默认类型
	}

	c.Header("Content-Type", contentType)
	c.Header("Cache-Control", "public, max-age=86400") // 缓存1天
	c.Header("Access-Control-Allow-Origin", "*")       // 允许跨域
	c.Header("Access-Control-Allow-Methods", "GET")
	c.Header("Access-Control-Allow-Headers", "Content-Type")

	// 如果有Content-Length，也设置上
	if contentLength := resp.Header.Get("Content-Length"); contentLength != "" {
		c.Header("Content-Length", contentLength)
	}

	// 返回图片数据
	_, err = io.Copy(c.Response.BodyWriter(), resp.Body)
	if err != nil {
		log.Printf("Failed to copy image data for URL %s: %v", imageURL, err)
		return
	}

	log.Printf("Successfully proxied image: %s", imageURL)
}
