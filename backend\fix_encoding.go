package main

import (
	"fmt"
	"log"
	"unicode/utf8"
	"xr-gallery/internal/config"
	"xr-gallery/internal/database"
	"xr-gallery/internal/model"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig(config.GetConfigPath())
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化数据库
	if err := database.InitDB(cfg.Database); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer database.CloseDB()

	// 获取数据库实例
	db := database.GetDB()

	// 查询一条记录
	var gallery model.XR
	if err := db.Where("xrid = ?", 17740).First(&gallery).Error; err != nil {
		log.Fatalf("Failed to query gallery: %v", err)
	}

	fmt.Printf("Original title: %s\n", gallery.Title)
	
	// 尝试修复双重编码问题
	fixed := fixDoubleEncoding(gallery.Title)
	fmt.Printf("Fixed title: %s\n", fixed)
	fmt.Printf("Is valid UTF-8: %v\n", utf8.ValidString(fixed))
}

// fixDoubleEncoding 尝试修复双重UTF-8编码问题
func fixDoubleEncoding(input string) string {
	// 将字符串转换为字节数组
	bytes := []byte(input)
	
	// 尝试将UTF-8字节重新解释为Latin1，然后再解码为UTF-8
	// 这是修复"UTF-8 -> Latin1 -> UTF-8"双重编码的常见方法
	
	// 第一步：将UTF-8字节序列重新解释为Latin1字符
	latin1Bytes := make([]byte, 0, len(bytes))
	for i := 0; i < len(bytes); {
		r, size := utf8.DecodeRune(bytes[i:])
		if r == utf8.RuneError {
			// 如果不是有效的UTF-8，直接复制字节
			latin1Bytes = append(latin1Bytes, bytes[i])
			i++
		} else if r < 256 {
			// 如果是Latin1范围内的字符，直接转换
			latin1Bytes = append(latin1Bytes, byte(r))
			i += size
		} else {
			// 如果是超出Latin1范围的字符，可能是正确的UTF-8，保持原样
			for j := 0; j < size; j++ {
				latin1Bytes = append(latin1Bytes, bytes[i+j])
			}
			i += size
		}
	}
	
	// 第二步：将Latin1字节重新解释为UTF-8
	result := string(latin1Bytes)
	
	// 验证结果是否是有效的UTF-8
	if utf8.ValidString(result) {
		return result
	}
	
	// 如果修复失败，返回原始字符串
	return input
}
