package main

import (
	"fmt"
	"log"
	"unicode/utf8"
	"xr-gallery/internal/config"
	"xr-gallery/internal/database"
	"xr-gallery/internal/model"

	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig(config.GetConfigPath())
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化数据库
	if err := database.InitDB(cfg.Database); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer database.CloseDB()

	// 获取数据库实例
	db := database.GetDB()

	// 查询一条记录
	var gallery model.XR
	if err := db.Where("xrid = ?", 17740).First(&gallery).Error; err != nil {
		log.Fatalf("Failed to query gallery: %v", err)
	}

	fmt.Printf("Original title: %s\n", gallery.Title)
	fmt.Printf("Title bytes: %v\n", []byte(gallery.Title))
	fmt.Printf("Title length: %d\n", len(gallery.Title))
	fmt.Printf("Title rune count: %d\n", utf8.RuneCountInString(gallery.Title))
	fmt.Printf("Is valid UTF-8: %v\n", utf8.ValidString(gallery.Title))

	// 尝试不同的编码转换
	fmt.Println("\n--- Encoding Tests ---")

	// 测试是否是GBK编码
	testGBKToUTF8(gallery.Title)

	// 测试是否是Latin1编码
	testLatin1ToUTF8(gallery.Title)
}

func testGBKToUTF8(input string) {
	fmt.Println("\n--- Testing GBK to UTF-8 conversion ---")

	// 尝试将输入作为GBK解码为UTF-8
	decoder := simplifiedchinese.GBK.NewDecoder()
	result, _, err := transform.String(decoder, input)
	if err != nil {
		fmt.Printf("GBK decode error: %v\n", err)
		return
	}

	fmt.Printf("GBK decoded result: %s\n", result)
	fmt.Printf("Is valid UTF-8: %v\n", utf8.ValidString(result))
}

func testLatin1ToUTF8(input string) {
	fmt.Println("\n--- Testing Latin1 to UTF-8 conversion ---")

	// 尝试将输入作为Latin1转换为UTF-8
	// Latin1的每个字节直接对应Unicode码点
	runes := make([]rune, len(input))
	for i, b := range []byte(input) {
		runes[i] = rune(b)
	}
	result := string(runes)

	fmt.Printf("Latin1 converted result: %s\n", result)
	fmt.Printf("Is valid UTF-8: %v\n", utf8.ValidString(result))
}
