package model

import (
	"time"
)

// XR represents a gallery item
type XR struct {
	ID            int       `json:"id"`
	Title         string    `json:"title"`
	Description   string    `json:"description"`
	CoverImageURL string    `json:"cover_image_url"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	Images        []XRInfo  `json:"images,omitempty"` // For detail view
}

// XRInfo represents an image within a gallery
type XRInfo struct {
	ID         int    `json:"id"`	
	XRID       int    `json:"xr_id"`
	ImageURL   string `json:"image_url"`
	Description string `json:"description"`
	OrderIndex int    `json:"order_index"`
}

// Pagination represents pagination information
type Pagination struct {
	CurrentPage int `json:"current_page"`
	PerPage     int `json:"per_page"`
	Total       int `json:"total"`
	TotalPages  int `json:"total_pages"`
}

// GalleryListResponse is the response structure for listing galleries
type GalleryListResponse struct {
	List       []XR       `json:"list"`
	Pagination Pagination `json:"pagination"`
}

// Response is a generic API response structure
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}
