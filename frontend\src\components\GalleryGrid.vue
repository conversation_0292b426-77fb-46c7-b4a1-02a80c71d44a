<template>
  <div class="gallery-grid-container">
    <!-- 图库网格 -->
    <div class="gallery-grid" :class="{ 'loading': loading }">
      <div
        v-for="(gallery, index) in galleries"
        :key="gallery.id"
        class="gallery-item"
        @click="$emit('itemClick', gallery.xrid)"
      >
        <!-- 图片容器 -->
        <div class="image-container">
          <!-- 直接显示图片 -->
          <img
            :src="gallery.cover"
            :alt="gallery.title"
            class="gallery-image"
            loading="lazy"
          />

          <!-- 图片数量标签 -->
          <div class="image-count-badge">
            <n-tag size="small" type="info">
              {{ gallery.image_count }}张
            </n-tag>
          </div>
        </div>
        
        <!-- 图库信息 -->
        <div class="gallery-info">
          <h3 class="gallery-title" :title="gallery.title">
            {{ gallery.title }}
          </h3>
          <div class="gallery-meta">
            <n-tag size="small" type="default" class="xrid-tag">
              ID: {{ gallery.xrid }}
            </n-tag>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 加载更多指示器 -->
    <div v-if="loading" class="loading-more">
      <n-spin size="medium">
        <template #description>
          <span>加载中...</span>
        </template>
      </n-spin>
    </div>
  </div>
</template>

<script setup>
import { NTag } from 'naive-ui'

const props = defineProps({
  galleries: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['itemClick'])
</script>

<style scoped>
.gallery-grid-container {
  width: 100%;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  transition: opacity 0.3s ease;
}

.gallery-grid.loading {
  opacity: 0.8;
}

.gallery-item {
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
  cursor: pointer;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.5s ease forwards;
}

.gallery-item:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.gallery-item.loaded {
  opacity: 1;
  transform: translateY(0);
}

.image-container {
  position: relative;
  aspect-ratio: 3/4;
  overflow: hidden;
  background: var(--bg-secondary);
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: var(--spacing-sm);
  color: var(--text-tertiary);
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.placeholder-text {
  font-size: 0.8rem;
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-image {
  transform: scale(1.05);
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-count-badge {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
}

.gallery-info {
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.gallery-title {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-primary);
  line-height: 1.4;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.gallery-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.xrid-tag {
  font-size: 0.75rem;
}

.loading-more {
  display: flex;
  justify-content: center;
  padding: var(--spacing-xl);
}

/* 响应式设计 */
@media (max-width: 1280px) {
  .gallery-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 1024px) {
  .gallery-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .gallery-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
  }
  
  .gallery-title {
    font-size: 0.8rem;
    -webkit-line-clamp: 1;
  }
  
  .gallery-info {
    padding: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .gallery-grid {
    grid-template-columns: 1fr;
  }
}

/* 动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 延迟动画 */
.gallery-item:nth-child(1) { animation-delay: 0.1s; }
.gallery-item:nth-child(2) { animation-delay: 0.2s; }
.gallery-item:nth-child(3) { animation-delay: 0.3s; }
.gallery-item:nth-child(4) { animation-delay: 0.4s; }
.gallery-item:nth-child(5) { animation-delay: 0.5s; }
.gallery-item:nth-child(n+6) { animation-delay: 0.6s; }
</style>
