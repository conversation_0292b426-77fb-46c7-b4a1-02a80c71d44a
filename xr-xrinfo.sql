CREATE TABLE `xr` (
  `id` int(8) NOT NULL AUTO_INCREMENT,
  `xrid` int(11) NOT NULL,
  `issave` tinyint(4) NOT NULL DEFAULT '0',
  `fm` varchar(255) DEFAULT NULL,
  `refm` varchar(255) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `xr_xrid_key` (`xrid`)
) ENGINE=InnoDB AUTO_INCREMENT=25407 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT;

CREATE TABLE `xrinfo` (
  `id` int(8) NOT NULL AUTO_INCREMENT,
  `xrid` int(11) NOT NULL,
  `ourl` varchar(100) DEFAULT NULL,
  `reurl` varchar(255) DEFAULT NULL,
  PRIMARY <PERSON>Y (`id`) USING BTREE,
  UNIQUE KEY `xrinfo_ourl_key` (`ourl`),
  KEY `idx_xrid` (`xrid`) USING BTREE,
  KEY `idx_xrinfo_xrid` (`xrid`),
  CONSTRAINT `xrinfo_xrid_fkey` FOREIGN KEY (`xrid`) REFERENCES `xr` (`xrid`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=752448 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT;