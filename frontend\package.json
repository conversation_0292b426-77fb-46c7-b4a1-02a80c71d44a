{"name": "xr-gallery-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"preinstall": "npx only-allow pnpm", "dev": "vite", "build": "vite build", "preview": "vite preview"}, "engines": {"node": ">=16.0.0", "pnpm": ">=7.0.0"}, "dependencies": {"@vicons/ionicons5": "^0.13.0", "@vicons/tabler": "^0.13.0", "@vueuse/core": "^10.7.0", "naive-ui": "^2.38.0", "pinia": "^2.1.0", "vue": "^3.4.0", "vue-router": "^4.2.0"}, "devDependencies": {"@playwright/test": "^1.54.1", "@vitejs/plugin-vue": "^5.0.0", "only-allow": "^1.2.1", "playwright": "^1.54.1", "vite": "^5.0.0"}}